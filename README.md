# Thông tin dự án

Đây là 1 ứng dụng CLI đơn giản được viết bằng Rust phiên bản mới nhất hiện tại, các thư viện và công cụ được sử dụng cho dự án này cũng là những thư viện, công cụ mới nhất và được cộng đồng đánh giá cao và tin tưởng. Ứng dùng này dùng để lắng nghe các giao dịch mới nhất trên blockchain Solana mà trong giao dịch có chứa các địa chỉ ví trong config, và là giao dịch thanh toán của <PERSON>lio (https://www.hel.io/), sau đó giải mã giao dịch để lấy thông tin về người thanh toán và thông báo cho người dùng qua JSON-RPC websocket.

# Luồng hoạt động

1. Kết nối đến dịch vụ grpc transaction streaming
2. Gửi 1 subscribe request đến dịch vụ transaction streaming về các địa chỉ ví sẽ listen
3. Lắng nghe các giao dịch được gửi đến từ server
4. Khi nhận được giao dịch mới, kiểm tra giao dịch đó, nếu đúng là giao dịch theo yêu cầu thì thực hiện giải mã transaction logs, hoặc instructions để lấy các thông tin cần thiết
5. In thông tin về giao dịch đó ra console cũng như gửi cho người dùng qua websocket

# Chức năng chính

- Hỗ trợ nhiều dịch vụ Transaction Streaming khác nhau (YellowStone Geyser GRPC, Corvus Aurifex RPC (aRPC),...)
- Hỗ trợ lắng nghe từ nhiều dịch vụ Transaction Streaming cùng 1 lúc, nhằm đảm bảo nhận được giao dịch 1 cách nhanh nhất (vẫn đảm bảo được các giao dịch không bị trùng lặp)
- Hỗ trợ giải mã giao dịch bằng cả transaction logs hoặc instructions
- Thông báo qua websocket
- Tự động generate ra GRPC Client cho các dịch vụ Transaction Streaming từ file proto, chuẩn hóa các class, interface, implement,... để dễ dàng thêm các dịch vụ khác sau này
- Các file proto sẽ được nhúng vào dự án theo dạng gitsubmodule, liên kết trực tiếp đến repo gốc của dịch vụ, giúp đảm bảo chúng ta luôn có file mới nhất

# Tài liệu

- [YellowStone Geyser GRPC Repository](https://github.com/rpcpool/yellowstone-grpc)
- [YellowStone Geyser GRPC Additional Documentation](https://docs.triton.one/project-yellowstone/dragons-mouth-grpc-subscriptions)
- [Corvus Aurifex RPC Repository](https://github.com/corvus-labs-io/aurifex)
- [Codama Repository](https://github.com/codama-idl/codama)
